{
  // Definition and documentation of all variables used by mycroft-core.
  //
  // Settings seen here are considered DEFAULT.  Settings can also be
  // overridden at the REMOTE level (set by the user via
  // https://home.mycroft.ai), at the SYSTEM level (typically in the file
  // '/etc/mycroft/mycroft.conf'), or at the USER level (typically in the
  // file '~/.config/mycroft/mycroft.conf').
  //
  // The load order of settings is:
  //   DEFAULT
  //   REMOTE
  //   SYSTEM
  //   USER
  //
  // The Override: comments below indicates where these settings are generally
  // set outside of this file.  The load order is always followed, so an
  // individual systems can still apply changes at the SYSTEM or USER levels.

  // Language used for speech-to-text and text-to-speech.
  // Code is a BCP-47 identifier (https://tools.ietf.org/html/bcp47), lowercased
  // TODO: save unmodified, lowercase upon demand
  "lang": "en-us",

  // Measurement units, either 'metric' or 'english'
  // Override: REMOTE
  "system_unit": "metric",

  // Time format, either 'half' (e.g. "11:37 pm") or 'full' (e.g. "23:37")
  // Override: REMOTE
  "time_format": "half",

  // Date format, either 'MDY' (e.g. "11-29-1978") or 'DMY' (e.g. "29-11-1978")
  // Override: REMOTE
  "date_format": "MDY",

  // Whether to opt in to data collection
  // Override: REMOTE
  "opt_in": false,

  // Play a beep when system begins to listen?
  "confirm_listening": true,

  // File locations of sounds to play for system events
  "sounds": {
    "start_listening": "snd/start_listening.wav",
    "end_listening": "snd/end_listening.wav",
    "acknowledge": "snd/acknowledge.mp3"
  },

  // Mechanism used to play WAV audio files
  // Override: SYSTEM
  "play_wav_cmdline": "paplay %1 --stream-name=mycroft-voice",

  // Mechanism used to play MP3 audio files
  // Override: SYSTEM
  "play_mp3_cmdline": "mpg123 %1",

  // Mechanism used to play OGG audio files
  // Override: SYSTEM
  "play_ogg_cmdline": "ogg123 -q %1",

  // Location where the system resides
  // NOTE: Although this is set here, an Enclosure can override the value.
  //       For example a mycroft-core running in a car could use the GPS.
  // Override: REMOTE
  "location": {
    "city": {
      "code": "Lawrence",
      "name": "Lawrence",
      "state": {
        "code": "KS",
        "name": "Kansas",
        "country": {
          "code": "US",
          "name": "United States"
        }
      }
    },
    "coordinate": {
      "latitude": 38.971669,
      "longitude": -95.23525
    },
    "timezone": {
      "code": "America/Chicago",
      "name": "Central Standard Time",
      "dstOffset": 3600000,
      "offset": -21600000
    }
  },

  // Also change in scripts/prepare-msm.sh
  "data_dir": "/opt/mycroft",

  // General skill values
  "skills": {
    "msm": {
      // Relative to "data_dir"
      "directory": "skills",
      "versioned": true,
      "repo": {
        // Relative to "data_dir"
        "cache": ".skills-repo",
        "url": "https://github.com/MycroftAI/mycroft-skills",
        "branch": "21.02"
      }
    },
    "upload_skill_manifest": true,
    // Directory to look for user skills
    "directory": "~/.local/share/mycroft/skills",
    // Enable auto update by msm
    "auto_update": true,
    // blacklisted skills to not load
    // NB: This is the basename() of the directory where the skill lives, so if
    // the skill you want to blacklist is in /opt/mycroft/skills/mycroft-alarm.mycroftai/
    // then you should write `["mycroft-alarm.mycroftai"]` below.
    "blacklisted_skills": [],
    // priority skills to be loaded first
    "priority_skills": ["mycroft-pairing", "mycroft-volume"],
    // Time between updating skills in hours
    "update_interval": 1.0
  },

  // Address of the REMOTE server
  "server": {
    "url": "https://api.mycroft.ai",
    "version": "v1",
    "update": true,
    "metrics": false,
    "sync_skill_settings": true
  },

  // The mycroft-core messagebus websocket
  "websocket": {
    "host": "0.0.0.0",
    "port": 8181,
    "route": "/core",
    "ssl": false
  },

  // The GUI messagebus websocket.  Once port is created per connected GUI
  "gui_websocket": {
        "host": "0.0.0.0",
        "base_port": 18181,
        "route": "/gui",
        "ssl": false
  },

  // URIs to use for testing network connection.
  "network_tests": {
    "dns_primary": "*******",
    "dns_secondary": "*******",
    "web_url": "https://www.google.com",
    "ncsi_endpoint": "http://www.msftncsi.com/ncsi.txt",
    "ncsi_expected_text": "Microsoft NCSI"
  },

  // Settings used by the wake-up-word listener
  // Override: REMOTE
  "listener": {
    "sample_rate": 16000,
    // Set 'save_path' to configure the location of files stored if
    // 'record_wake_words' and/or 'save_utterances' are set to 'true'.
    // WARNING: Make sure that user 'mycroft' has write-access on the
    // directory!
    // "save_path": "/tmp",
    // Set 'record_wake_words' to save a copy of wake word triggers
    // as .wav files under: /'save_path'/mycroft_wake_words
    "record_wake_words": false,
    // Set 'save_utterances' to save each sentence sent to STT -- by default
    // they are only kept briefly in-memory.  This can be useful for for
    // debugging or other custom purposes.  Recordings are saved
    // under: /'save_path'/mycroft_utterances/<TIMESTAMP>.wav
    "save_utterances": false,
    "wake_word_upload": {
      "disable": false,
      "url": "https://training.mycroft.ai/precise/upload"
    },

    // Override as SYSTEM or USER to select a specific microphone input instead of
    // the PortAudio default input.
    //   "device_name": "somename",  // can be regex pattern or substring
    //       or
    //   "device_index": 12,

    // Stop listing to the microphone during playback to prevent accidental triggering
    // This is enabled by default, but instances with good microphone noise cancellation
    // can disable this to listen all the time, allowing 'barge in' functionality.
    "mute_during_output" : true,

    // How much (if at all) to 'duck' the speaker output during listening.  A
    // setting of 0.0 will not duck at all.  A 1.0 will completely mute output
    // while in a listening state.  Values in between will lower the volume
    // partially (this is optional behavior, depending on the enclosure).
    "duck_while_listening" : 0.3,

    // In milliseconds
    "phoneme_duration": 120,
    "multiplier": 1.0,
    "energy_ratio": 1.5,
    "wake_word": "hey mycroft",
    "stand_up_word": "wake up",

    // Settings used by microphone to set recording timeout
    "recording_timeout": 10.0,
    "recording_timeout_with_silence": 3.0
  },

  // Settings used for any precise wake words
  "precise": {
    "use_precise": true,
    "dist_url": "https://github.com/MycroftAI/precise-data/raw/dist/{arch}/latest",
    "model_url": "https://raw.githubusercontent.com/MycroftAI/precise-data/models/{wake_word}.tar.gz"
  },

  // Hotword configurations
  "hotwords": {
    "hey mycroft": {
        "module": "precise",
        "phonemes": "HH EY . M AY K R AO F T",
        "threshold": 1e-90,
        "lang": "en-us"
        // Specify custom model via:
        // "local_model_file": "~/.local/share/mycroft/precise/models/something.pb"
        // Precise options:
        // "sensitivity": 0.5,  // Higher = more sensitive
        // "trigger_level": 3   // Higher = more delay & less sensitive
        },

    "wake up": {
        "module": "pocketsphinx",
        "phonemes": "W EY K . AH P",
        "threshold": 1e-20,
        "lang": "en-us"
        }
  },

  // Mark 1 enclosure settings
  // Override: SYSTEM (e.g. Picroft)
  "enclosure": {
    // Platform name
    // Options: 'picroft', 'mycroft_mark_1'
    // Override: SYSTEM (set by specific enclosures)
    // "platform": "picroft",
    // "platform_enclosure_path": "/etc/myenclosure/code.py",

    // COMM params to the Arduino/faceplate
    "port": "/dev/ttyAMA0",
    "rate": 9600,
    "timeout": 5.0,

    // ??
    "update": true,

    // Run a self test at bootup?
    "test": false
  },

  // Level of logs to store, one of  "CRITICAL", "ERROR", "WARNING", "INFO", "DEBUG"
  // NOTE: This configuration setting is special and can only be changed in the
  // SYSTEM or USER configuration file, it will not be read if defined in the
  // DEFAULT (here) or in the REMOTE mycroft config.
  // If not defined, the default log level is INFO.
  //"log_level": "INFO",

  // Format of logs to store.
  // NOTE: This configuration setting is special and can only be changed in the
  // SYSTEM or USER configuration file, it will not be read if defined in the
  // DEFAULT (here) or in the REMOTE mycroft config.
  // If not defined, the default log format is:
  // {asctime} | {levelname:8} | {process:5} | {name} | {message}
  //"log_format": "{asctime} | {levelname:8} | {process:5} | {name} | {message}",

  // Messagebus types that will NOT be output to logs
  "ignore_logs": ["enclosure.mouth.viseme", "enclosure.mouth.display"],

  // Settings related to remote sessions
  // Overrride: none
  "session": {
    // Time To Live, in seconds
    "ttl": 180
  },

  // Speech to Text parameters
  // Override: REMOTE
  "stt": {
    // Engine.  Options: "mycroft", "google", "wit", "ibm", "kaldi", "bing",
    //                   "houndify", "deepspeech_server", "govivace", "yandex"
    "module": "mycroft"
    // "deepspeech_server": {
    //   "uri": "http://localhost:8080/stt"
    // },
    // "kaldi": {
    //   "uri": "http://localhost:8080/client/dynamic/recognize"
    // },
    //"govivace": {
    //   "uri": "https://services.govivace.com:49149/telephony",
    //   "credential": {
    //      "token": "xxxxx"
    //      }
    //}
  },

  // Text to Speech parameters
  // Override: REMOTE
  "tts": {
    // Engine.  Options: "mimic", "mimic2", "google", "marytts", "fatts", "espeak",
    // "spdsay", "yandex", "polly", "mozilla"
    "pulse_duck": false,
    "module": "mimic",
    "polly": {
      "voice": "Matthew",
      "region": "us-east-1",
      "access_key_id": "",
      "secret_access_key": ""
    },
    "mimic": {
      "voice": "ap"
    },
    "mimic2": {
      "lang": "en-us",
      "url": "https://mimic-api.mycroft.ai/synthesize?text=",
      "preloaded_cache": "/opt/mycroft/preloaded_cache/Mimic2"
    },
    "espeak": {
      "lang": "english-us",
      "voice": "m1"
    },
    "mozilla": {
      "url": "http://0.0.0.0:5002"
    }
  },

  "padatious": {
    "intent_cache": "~/.local/share/mycroft/intent_cache",
    "train_delay": 4,
    "single_thread": false
  },

  "Audio": {
    "backends": {
      "local": {
        "type": "simple",
        "active": true
      },
      "vlc": {
        "type": "vlc",
        "active": true,
        "duck": true
      }
    },
    "default-backend": "local"
  },

  "debug": false
}
