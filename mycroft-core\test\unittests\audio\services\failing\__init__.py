# Copyright 2017 Mycroft AI Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
from mycroft.audio.services import AudioBackend


class FailingBackend(AudioBackend):
    def __init__(self, config, emitter, name='Failing'):
        raise Exception

    def supported_uris(self):
        return ['file', 'http']


def load_service(base_config, emitter):
    instances = [FailingBackend(base_config, emitter)]
    return instances
