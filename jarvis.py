import os
import json
import queue
import threading
import pyttsx3
import pyaudio
import vosk
import tkinter as tk
from tkinter import scrolledtext
from PIL import Image, ImageTk

# Initialize Text-to-Speech Engine
engine = pyttsx3.init()
engine.setProperty('rate', 150)

# Load Vosk Model
vosk_model = "model"
if not os.path.exists(vosk_model):
    print("Please download a Vosk model and extract it into 'model' directory.")
    exit(1)

model = vosk.Model(vosk_model)
recognizer = vosk.KaldiRecognizer(model, 16000)

# Setup Audio Input
audio_queue = queue.Queue()

def audio_callback(in_data, frame_count, time_info, status):
    audio_queue.put(in_data)
    return None, pyaudio.paContinue

audio = pyaudio.PyAudio()
stream = audio.open(format=pyaudio.paInt16, channels=1,
                    rate=16000, input=True, frames_per_buffer=8192,
                    stream_callback=audio_callback)
stream.start_stream()

# GUI Class
class JarvisGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Jarvis - AI Voice Assistant")

        # Load and display an image (Optional)
        try:
            self.image = Image.open("ui.gif").resize((200, 200))
            self.photo = ImageTk.PhotoImage(self.image)
            self.img_label = tk.Label(root, image=self.photo)
            self.img_label.pack()
        except FileNotFoundError:
            # If no image file found, just show a text label
            self.img_label = tk.Label(root, text="JARVIS AI", font=("Arial", 16, "bold"))
            self.img_label.pack()

        # Display Recognized Text
        self.text_area = scrolledtext.ScrolledText(root, height=10, width=50)
        self.text_area.pack(pady=10)

        # Buttons
        self.listen_button = tk.Button(root, text="Start Listening", command=self.start_listening, bg="green", fg="white")
        self.listen_button.pack(pady=5)

        self.stop_button = tk.Button(root, text="Stop Listening", command=self.stop_listening, bg="red", fg="white")
        self.stop_button.pack(pady=5)

        self.running = False

    def start_listening(self):
        self.running = True
        self.listen_button.config(state=tk.DISABLED)
        threading.Thread(target=self.recognize_speech, daemon=True).start()

    def stop_listening(self):
        self.running = False
        self.listen_button.config(state=tk.NORMAL)

    def recognize_speech(self):
        while self.running:
            if not audio_queue.empty():
                data = audio_queue.get()
                if recognizer.AcceptWaveform(data):
                    result = json.loads(recognizer.Result())["text"]
                    self.display_and_respond(result)

    def display_and_respond(self, text):
        if text:
            self.text_area.insert(tk.END, "You: " + text + "\n")
            self.text_area.see(tk.END)

            response = self.process_command(text)
            self.text_area.insert(tk.END, "Jarvis: " + response + "\n")
            self.text_area.see(tk.END)

            self.speak(response)

    def process_command(self, text):
        """Processes user commands."""
        if "hello" in text:
            return "Hello! How can I assist you?"
        elif "open notepad" in text:
            os.system("notepad.exe")
            return "Opening Notepad."
        elif "exit" in text:
            self.root.quit()
        else:
            return "I didn't understand that."

    def speak(self, text):
        """Uses pyttsx3 to make the assistant speak."""
        engine.say(text)
        engine.runAndWait()

# Run GUI
root = tk.Tk()
jarvis = JarvisGUI(root)
root.mainloop()
