# Perform linting, code style checking, and unit tests on mycroft-core
#
# This is only part of the continuous integration (CI) process for mycroft-core
# behavioral tests (Voight-Kampff) are run in a Jenkins job.
name: Unit Tests

on:
  push:
    branches:
      - dev
      - master
  pull_request:
    branches:
      - dev
      - master

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        # Run for all versions of Python officially supported by mycroft-core
        python-version: [3.6, 3.7, 3.8, 3.9]
    steps:
    - uses: actions/checkout@v2
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v2
      with:
        python-version: ${{ matrix.python-version }}
    - name: Cache pip packages
      uses: actions/cache@v2
      env:
        cache-name: cache-pypi-modules
      with:
        # pip cache files are stored in `~/.cache/pip` on Linux
        path: ~/.cache/pip
        key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('requirements/requirements.txt', 'requirements/tests.txt') }}
        restore-keys: |
          ${{ runner.os }}-build-${{ env.cache-name }}-
          ${{ runner.os }}-build-
          ${{ runner.os }}-
    - name: Install compilers
      run: |
        sudo apt-get update -qq
        sudo apt-get install -y gcc g++
    - name: Install Python packages for testing
      run: pip install -r requirements/tests.txt
    - name: Setup mycroft core
      # Only build mimic for one Python version; others should not differ
      run: |
        if [[ ${{ matrix.python-version }} == 3.9 ]]; then ./dev_setup.sh; fi
        if [[ ${{ matrix.python-version }} != 3.9 ]]; then ./dev_setup.sh -sm; fi
      env:
        CI: true
    - name: Linting and code style
      run: |
        pycodestyle mycroft test
        flake8 mycroft test --count --select=E9,F63,F7,F82 --show-source --statistics
    - name: Run unit tests
      # Only generate the html report for one of the versions, others should not differ
      run: |
        if [[ ${{ matrix.python-version }} == 3.9 ]]; then ./start-mycroft.sh unittest --cov-report html; fi
        if [[ ${{ matrix.python-version }} != 3.9 ]]; then ./start-mycroft.sh unittest; fi
    - name: Upload code coverage
      # Only upload the report for the Python version that generated it
      run: if [[ ${{ matrix.python-version }} == 3.9 ]]; then bash <(curl -s https://codecov.io/bash); fi
      env:
        CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
  shellcheck:
    name: Shellcheck
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Shell check mycroft tools
      uses: ludeeus/action-shellcheck@203a3fd018dfe73f8ae7e3aa8da2c149a5f41c33
      env:
        SHELLCHECK_OPTS: -x
      with:
        ignore_names: 'run_test_suite.sh'
#        ignore_paths: doc mycroft test requirements
