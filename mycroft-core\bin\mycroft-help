#!/usr/bin/env bash

# Copyright 2018 Mycroft AI Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

SOURCE="${BASH_SOURCE[0]}"
cd -P "$( dirname "$SOURCE" )"/.. || exit
DIR="$( pwd )"

echo -e "\\e[36m<PERSON><PERSON>croft\\e[0m is your open source voice assistant.  Full source"
echo "can be found at: ${DIR}"
echo
echo "Mycroft-specific commands you can use from the Linux command prompt:"
echo "  mycroft-cli-client       Command line client, useful for debugging"
echo "  mycroft-msm              Mycroft Skills Manager, to manage your Skills"
echo "  mycroft-start            Launch/restart Mycroft services"
echo "  mycroft-stop             Stop Mycroft services"
echo
echo "Scripting Utilities:"
echo "  mycroft-listen           Activate the microphone to listen for a command"
echo "  mycroft-speak <phr>      Have Mycroft speak a phrase to the user"
echo "  mycroft-say-to <utt>     Send an utterance to Mycroft as if spoken by a user"
echo
echo "Mycroft's Python Virtual Environment (venv) control:"
echo "  mycroft-pip              Install a Python package within the venv"
echo "  mycroft-venv-activate    Enter the venv"
echo "  mycroft-venv-deactivate  Exit the venv"
echo
echo "Skill Development:"
echo "  mycroft-msk              Mycroft Skills Kit, create and share Skills"
echo "  mycroft-skill-testrunner Run integration tests on Mycroft Skills"
echo
echo "Other:"
echo "  mycroft-config           Manage your local Mycroft configuration files"
echo "  mycroft-mic-test         Record and playback to directly test microphone"
echo "  mycroft-help             Display this message"
echo
echo "For more information, see https://mycroft.ai/documentation"
