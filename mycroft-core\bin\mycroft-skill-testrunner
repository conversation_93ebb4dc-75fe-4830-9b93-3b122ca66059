#!/usr/bin/env bash

# Copyright 2018 Mycroft AI Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

SOURCE="${BASH_SOURCE[0]}"
DIR="$( dirname "$SOURCE" )"

# Enter the Mycroft venv
source "$DIR/../venv-activate.sh" -q

function count-files() {
    # shellcheck disable=SC2012
    ls "$1" | wc -l
}

function vktest-clear() {
    FEATURES_DIR="$DIR/../test/integrationtests/voight_kampff/features"
    num_feature_files=$(count-files "$FEATURES_DIR")
    # A clean directory will have `steps/` and `environment.py`
    if [ "$num_feature_files" -gt "2" ] ; then
        echo "Removing Feature files..."
        rm -f "${DIR}"/../test/integrationtests/voight_kampff/features/*.feature
        rm -f "${DIR}"/../test/integrationtests/voight_kampff/features/*.config.json
    fi
    STEPS_DIR="$FEATURES_DIR/steps"
    num_steps_files=$(count-files "$STEPS_DIR")
    if [ "$num_steps_files" -gt "2" ] ; then
        echo "Removing Custom Step files..."
        TMP_DIR="$STEPS_DIR/tmp"
        mkdir "$TMP_DIR"
        mv "$STEPS_DIR/configuration.py" "$TMP_DIR"
        mv "$STEPS_DIR/utterance_responses.py" "$TMP_DIR"
        rm -f "${STEPS_DIR}"/*.py
        mv "${TMP_DIR}"/* "$STEPS_DIR"
        rmdir "$TMP_DIR"
    fi
    echo "Voight Kampff tests clear."
}

# Invoke the individual skill tester
if [ "$#" -eq 0 ] ; then
    python -m test.integrationtests.skills.runner .
elif [ "$1" = "vktest" ] ; then
    if [ "$2" = "clear" ] ; then
        vktest-clear
    else
        shift
        python -m test.integrationtests.voight_kampff "$@"
    fi
else
    python -m test.integrationtests.skills.runner "$@"
fi
