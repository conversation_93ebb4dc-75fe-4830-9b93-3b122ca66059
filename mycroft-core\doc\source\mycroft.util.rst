mycroft.util package
====================

The mycroft.util package includes functions for common operations such as
playing audio files, parsting and creating natural text as well as many
components used internally in Mycroft such as cache directory lookup,
path resolution. etc.

Below _some_ of the functions that are of interest to skill developers are
listed.

LOG
---
.. autofunction:: mycroft.util.LOG

play_wav
---------
.. autofunction:: mycroft.util.play_wav

play_mp3
---------
.. autofunction:: mycroft.util.play_mp3

play_ogg
---------
.. autofunction:: mycroft.util.play_ogg

resolve_resource_file
---------------------
.. autofunction:: mycroft.util.resolve_resource_file

get_cache_directory
-------------------
.. autofunction:: mycroft.util.get_cache_directory


